import frappe
from frappe import _


def populate_delivery_type(doc, method=None):
    """
    Populate Delivery Type and Sales Order reference on Work Order based on the source document.
    This function is called via doc_events hook before_save.

    Args:
        doc: Work Order document
        method: Hook method (not used)
    """

    # First, try to inherit Sales Order reference from parent item if this is a child part
    if not doc.sales_order:
        _populate_sales_order_from_parent(doc)

    # Then populate delivery type if we have a sales order
    if not doc.delivery_type and doc.sales_order:
        try:
            sales_order = frappe.get_doc("Sales Order", doc.sales_order)
            if sales_order.get("delivery_type"):
                doc.delivery_type = sales_order.delivery_type
        except Exception as e:
            frappe.log_error(
                title="Error populating delivery type on Work Order",
                message=f"Work Order: {doc.name}\nSales Order: {doc.sales_order}\nError: {str(e)}",
            )

    # Populate internal and external notes from Velocetec Line Costing
    # _populate_notes_from_velocetec_costing(doc)


def populate_notes_from_vlc(doc, method=None):
    """
    Populate internal and external notes from Velocetec Line Costing table.

    Args:
        doc: Work Order document
    """
    try:
        # Determine which sales order to use for fetching Velocetec costing
        sales_order_to_use = doc.sales_order or doc.get("sales_order_child")
        

        if sales_order_to_use:
            # Get the Velocetec costing reference from the sales order
            sales_order_vc = frappe.db.get_value(
                "Sales Order", sales_order_to_use, "velocetec_costing"
            )

            if sales_order_vc:
                # Fetch internal and external notes from Velocetec Line Costing
               notes_data = frappe.db.sql(
                        """
                        SELECT name, internal_notes, FROM `tabVelocetec Costing Detail`
                        WHERE parent = %s AND part_number = %s
                        LIMIT 1
                    """,
                        (sales_order_vc, doc.production_item),
                        as_dict=True,
                    )

            frappe.throw(str(notes_data))
            if notes_data:
                internal_notes = notes_data.internal_notes
                external_notes = notes_data.external_notes

                if doc.sales_order:
                    # If work order has sales_order, set notes in main document
                    if internal_notes:
                        doc.custom_internal_notes = internal_notes
                    if external_notes:
                        doc.custom_external_notes = external_notes
                else:
                    # If work order has sales_order_child, set notes in main document
                    if internal_notes:
                        doc.custom_internal_notes = internal_notes
                    if external_notes:
                        doc.custom_external_notes = external_notes

    except Exception as e:
        frappe.log_error(
            title="Error populating notes from Velocetec Line Costing",
            message=f"Work Order: {doc.name}\nProduction Item: {doc.production_item}\nError: {str(e)}",
        )

def _populate_sales_order_from_parent(doc):
    """
    Populate Sales Order reference for child parts by using Production Plan relationship.

    Args:
        doc: Work Order document
    """
    if not doc.production_item:
        return

    _find_sales_order_via_production_plan(doc)


def _find_sales_order_via_production_plan(doc):
    """
    Find Sales Order reference for child parts using Production Plan relationship.

    Args:
        doc: Work Order document
    """
    try:
        # Step 1: Check if item is directly in Production Plan Item table (parent items)
        production_plans = frappe.db.sql(
            """
            SELECT DISTINCT pp.name as production_plan_name, ppi.sales_order
            FROM `tabProduction Plan` pp
            INNER JOIN `tabProduction Plan Item` ppi ON ppi.parent = pp.name
            WHERE ppi.item_code = %s
            AND pp.docstatus = 1
            ORDER BY pp.creation DESC
        """,
            (doc.production_item,),
            as_dict=True,
        )

        # Step 2: If not found as parent item, check Sub Assembly Item table (child parts)
        # Note: Sub Assembly Items don't have sales_order field, so we get it from Production Plan Item table
        if not production_plans:
            production_plans = frappe.db.sql(
                """
                SELECT DISTINCT pp.name as production_plan_name, ppi.sales_order
                FROM `tabProduction Plan` pp
                INNER JOIN `tabProduction Plan Sub Assembly Item` ppsa ON ppsa.parent = pp.name
                INNER JOIN `tabProduction Plan Item` ppi ON ppi.parent = pp.name
                WHERE ppsa.production_item = %s
                AND pp.docstatus = 1
                ORDER BY pp.creation DESC
            """,
                (doc.production_item,),
                as_dict=True,
            )

        for plan in production_plans:
            if plan.sales_order:
                sales_order_vc = frappe.db.get_value(
                    "Sales Order", plan.sales_order, "velocetec_costing"
                )

                if sales_order_vc:
                    item_in_vc = frappe.db.sql(
                        """
                        SELECT name FROM `tabVelocetec Costing Detail`
                        WHERE parent = %s AND part_number = %s
                        LIMIT 1
                    """,
                        (sales_order_vc, doc.production_item),
                        as_dict=True,
                    )

                    if item_in_vc:
                        doc.sales_order_child = plan.sales_order
                        frappe.db.set_value(
                            "Work Order",
                            doc.name,
                            "sales_order_child",
                            plan.sales_order,
                        )

                        return

    except Exception as e:
        frappe.log_error(
            title="Error finding Sales Order via Production Plan",
            message=f"Work Order: {doc.name}\nProduction Item: {doc.production_item}\nError: {str(e)}",
        )
